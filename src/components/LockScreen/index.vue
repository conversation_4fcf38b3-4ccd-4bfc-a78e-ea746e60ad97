<template>
  <div
    class="lock-screen-overlay fixed inset-0 flex flex-col items-center justify-center text-center bg-[url('@/assets/images/lock.jpg')] bg-cover bg-center"
  >
    <div class="absolute inset-0 bg-black/75 z-0" />
    <div
      class="relative z-1 p-8 max-w-sm rounded-xl shadow-lg bg-white/30 dark:bg-gray-700/40 backdrop-blur-lg text-center"
    >
      <h1 class="text-xl font-semibold mb-6 text-neutral-100">长时间没有操作，页面已锁定</h1>
      <el-form class="space-y-5" @submit.prevent="handleUnlock">
        <el-form-item class="m-0">
          <el-input
            v-model="password"
            type="password"
            show-password
            placeholder="请输入登录密码"
            size="large"
            @keyup.enter="handleUnlock"
          />
        </el-form-item>
        <el-form-item class="m-0">
          <el-button
            type="primary"
            size="large"
            class="w-full"
            :loading="isLoading"
            @click="handleUnlock"
          >
            <span class="i-carbon-unlocked mr-1 text-base align-middle" />
            解锁
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { useAppStore, useUserStore } from "@/store";
import { ElMessage } from "element-plus";
import Passport_Api from "@/api/passport";

const appStore = useAppStore();
const userStore = useUserStore();
const password = ref<string>("");
const isLoading = ref<boolean>(false);

/**
 * 处理解锁操作
 */
function handleUnlock() {
  if (!password.value.trim()) {
    ElMessage.error("请输入密码");
    return;
  }
  isLoading.value = true;
  Passport_Api.checkPassword({
    userId: userStore.userInfo.Id,
    password: password.value,
  })
    .then((res) => {
      if (res.Type === 200 && res.Data) {
        appStore.setLockState(false);
        password.value = ""; // 清空密码
      } else {
        ElMessage.error(res.Content);
      }
    })
    .finally(() => {
      isLoading.value = false;
    });
}

// 锁屏时添加body类名，解锁时移除
onMounted(() => {
  document.body.classList.add("lock-screen-active");
});

onUnmounted(() => {
  document.body.classList.remove("lock-screen-active");
});
</script>

<style scoped>
/* 锁屏覆盖层样式 */
.lock-screen-overlay {
  /* 使用极高的z-index确保覆盖所有Element Plus组件 */
  z-index: 999999 !important;
  /* 确保完全覆盖整个视口 */
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  /* 防止滚动 */
  overflow: hidden !important;
}
</style>

<style>
/* 全局样式：确保锁屏期间禁用页面滚动和交互 */
body.lock-screen-active {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  height: 100% !important;
}

/* 确保锁屏期间大部分Element Plus组件都在锁屏下方，但保持ElMessage可见 */
body.lock-screen-active .el-overlay,
body.lock-screen-active .el-dialog,
body.lock-screen-active .el-drawer,
body.lock-screen-active .el-message-box,
body.lock-screen-active .el-notification,
body.lock-screen-active .el-popover,
body.lock-screen-active .el-tooltip,
body.lock-screen-active .el-dropdown-menu,
body.lock-screen-active .el-select-dropdown,
body.lock-screen-active .el-picker-panel {
  z-index: 999998 !important;
}

/* 确保ElMessage在锁屏时仍然可见 */
body.lock-screen-active .el-message {
  z-index: 1000000 !important;
}

/* 防止锁屏期间的任何交互 */
body.lock-screen-active
  *:not(.lock-screen-overlay):not(.lock-screen-overlay *):not(.el-message):not(.el-message *) {
  pointer-events: none !important;
}

/* 恢复锁屏组件内的交互 */
.lock-screen-overlay,
.lock-screen-overlay * {
  pointer-events: auto !important;
}

/* 确保ElMessage的交互正常 */
body.lock-screen-active .el-message,
body.lock-screen-active .el-message * {
  pointer-events: auto !important;
}
</style>
