<template>
  <el-form :model="form" :rules="rules" inline :disabled="isPreview" scroll-to-error>
    <el-form-item label="名称" prop="Name">
      <el-input v-model="form.Name" link style="width: 150px" @blur="handleNameBlur" />
    </el-form-item>
    <el-form-item label="拼音码" prop="PYM">
      <el-input v-model="form.PYM" link style="width: 150px" />
    </el-form-item>
    <el-form-item label="编码" prop="Code">
      <el-input v-model="form.Code" link style="width: 150px" />
    </el-form-item>
    <el-form-item label="设备类别">
      <el-select v-model="form.DeviceType" style="width: 100px" filterable placeholder="分类">
        <el-option
          v-for="(item, index) in deviceTypeData"
          :key="'DT' + index"
          :label="item.Key"
          :value="item.Id"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="是否启用">
      <el-switch v-model="form.IsEnable" />
    </el-form-item>
    <el-form-item label="物联设备">
      <el-switch v-model="form.IsIOTDevice" />
    </el-form-item>
    <div v-if="form.IsIOTDevice">
      <el-form-item label="设备厂商">
        <el-select
          v-model="form.DeviceFactory"
          style="width: 120px"
          clearable
          placeholder="设备厂商"
          filterable
        >
          <el-option
            v-for="(item, index) in deviceFactoryData"
            :key="index"
            :label="item.Key"
            :value="item.Value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button size="small" type="primary" @click="handleAddParameter">添加参数</el-button>
      </el-form-item>
      <div v-for="(item, index) in form.Parameters" :key="'par' + index" class="parameters">
        <el-form-item label="参数名称">
          <el-input v-model="item.Name" link style="width: 150px" />
        </el-form-item>
        <el-form-item label="字段标记">
          <el-input v-model="item.SignCode" link style="width: 150px" />
        </el-form-item>
        <el-form-item label="参数类型">
          <el-select v-model="item.Type" style="width: 150px" placeholder="分类" filterable>
            <el-option
              v-for="(o, oIndex) in parTypeData"
              :key="'pt' + oIndex"
              :label="o.Key"
              :value="o.Id"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="item.Type === 1" label="选项值">
          <el-input
            v-model="item.OptionValue"
            link
            style="width: 380px"
            placeholder="每个选项值用 ‘,’ 分隔"
          />
          <span style="color: #ddd">（如：模式一:1,模式二:2）</span>
        </el-form-item>
        <el-form-item v-if="item.Type === 0" label="参数单位">
          <el-input v-model="item.Unit" link style="width: 150px" />
        </el-form-item>
        <el-form-item v-if="!isPreview" class="delete-btn" @click="handleDeleteParameter(index)">
          <el-icon><Delete /></el-icon>
          删除
        </el-form-item>
      </div>
    </div>
  </el-form>
</template>

<script setup lang="ts">
import { chineseToPinyin } from "@/utils";
import { FormRules } from "element-plus";
import { Delete } from "@element-plus/icons-vue";

const isPreview = inject("isPreview") as Ref<boolean>;
const rules = reactive<FormRules>({
  Name: [
    { required: true, message: "请输入名称", trigger: "blur" },
    { max: 25, message: "名称不能超过25个字符", trigger: "blur" },
  ],
  PYM: [
    { required: true, message: "请输入拼音码", trigger: "blur" },
    { max: 25, message: "拼音码不能超过25个字符", trigger: "blur" },
  ],
  Code: [
    { required: true, message: "请输入编码", trigger: "blur" },
    { max: 25, message: "编码不能超过25个字符", trigger: "blur" },
  ],
});
const form = ref<any>({
  Name: "",
  PYM: "",
  Code: "",
  DeviceType: 0,
  IsEnable: true,
  IsIOTDevice: false,
  Parameters: [],
});
const deviceTypeData = ref<{ Id: number; Key: string }[]>([
  {
    Id: 0,
    Key: "治疗",
  },
  {
    Id: 1,
    Key: "评定",
  },
]);
const deviceFactoryData = ref<{ Id: number; Key: string; Value: string }[]>([
  {
    Id: 1,
    Key: "VR",
    Value: "VR",
  },
  {
    Id: 2,
    Key: "OA治疗仪",
    Value: "OA",
  },
  {
    Id: 3,
    Key: "功率车",
    Value: "PowerCar",
  },
]);
const parTypeData = ref<{ Id: number; Key: string }[]>([
  {
    Id: 0,
    Key: "数值",
  },
  {
    Id: 1,
    Key: "选择",
  },
]);

const handleAddParameter = () => {
  form.value.Parameters.push({
    Name: "",
    Type: 1, // 参数参数   0数值  1选择
    Value: "", // 数值就是默认值 ，选择就是option
    OptionValue: "",
    Unit: "",
    SignCode: "", // 后端字段对应
  });
};

const handleDeleteParameter = (index: number) => {
  form.value.Parameters.splice(index, 1);
};

const handleProcessingData = (info: BaseInstrument) => {
  console.log(info);
};

const handleSubmit = () => {
  console.log("submit");
};

const handleNameBlur = () => {
  if (!form.value.Name) return;
  form.value.PYM = chineseToPinyin(form.value.Name);
};

interface Props {
  info: BaseInstrument | null;
}
const props = defineProps<Props>();

watch(
  () => props.info,
  (newVal) => {
    if (newVal) {
      handleProcessingData(newVal);
    }
  },
  { immediate: true }
);

defineExpose({
  handleSubmit,
});
</script>

<style lang="scss" scoped>
.parameters {
  width: 100%;
  position: relative;
  margin-bottom: 10px;
  border-bottom: 1px dashed #ddd;
  .el-form-item--mini.el-form-item {
    margin-bottom: 10px;
  }
  .delete-btn {
    padding: 0 8px;
    background-color: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    color: #f56c6c;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background-color: #f56c6c;
      color: #fff;
      border-color: #f56c6c;
      box-shadow: 0 2px 4px rgba(245, 108, 108, 0.3);
    }

    .el-icon {
      font-size: 14px;
    }
  }
}
</style>
