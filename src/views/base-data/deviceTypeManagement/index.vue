<template>
  <div class="app-container">
    <BaseTableSearchContainer @size-changed="tableResize">
      <template #search>
        <TBSearchContainer>
          <template #left>
            <el-form :model="queryParams" label-position="right" :inline="true">
              <el-form-item label="是否测试数据">
                <el-select
                  v-model="queryParams.isEnable"
                  placeholder="请选择"
                  clearable
                  :empty-values="[null, undefined, '']"
                  :value-on-clear="() => null"
                >
                  <el-option label="是" :value="true" />
                  <el-option label="否" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item label="关键字" prop="keywords">
                <el-input
                  v-model="queryParams.keywords"
                  placeholder="姓名/电话号码"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-form>
          </template>
          <template #right>
            <el-button type="primary" icon="search" @click="handleQuery">搜索</el-button>
            <el-button type="primary" @click="handlePreviewOrEdit(null, false)">新增</el-button>
          </template>
        </TBSearchContainer>
      </template>
      <template #table>
        <el-table
          ref="tableRef"
          v-loading="tableLoading"
          :data="pageData"
          :total="total"
          border
          row-key="Id"
          :height="tableFluidHeight"
          highlight-current-row
          style="text-align: center; flex: 1"
        >
          <el-table-column prop="Name" label="名称" align="center" />
          <el-table-column prop="Code" label="编码" align="center" />
          <el-table-column prop="PYM" label="拼音码" align="center" />
          <el-table-column prop="DeviceType" label="设备类别" align="center">
            <template #default="scope">
              {{ ["治疗", "评定"][scope.row.DeviceType] }}
            </template>
          </el-table-column>
          <el-table-column label="物联设备" align="center">
            <template #default="scope">
              {{ scope.row.IsIOTDevice ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="CreatedTime"
            label="创建时间"
            width="150"
            :formatter="tableDateFormat"
            align="center"
          />
          <el-table-column label="是否启用" align="center">
            <template #default="scope">
              {{ scope.row.IsEnable ? "是" : "否" }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" label="操作" align="center">
            <template #default="scope">
              <el-button link type="primary">查看</el-button>
              <el-button link type="primary">编辑</el-button>
              <el-button
                v-if="!scope.row.IsPublish"
                link
                type="primary"
                @click="handlePublish(scope.row)"
              >
                发布
              </el-button>
              <el-button
                v-if="!scope.row.IsPublish"
                link
                type="danger"
                @click="handleDelete(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template #pagination>
        <Pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.page"
          v-model:limit="queryParams.pageSize"
          @pagination="handleGetTableList"
        />
      </template>
    </BaseTableSearchContainer>
    <el-dialog v-model="showDialog" :title="dialogTitle" width="700" destroy-on-close>
      <DeviceTypeContent ref="deviceTypeContentRef" :info="currentItemInfo" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button type="primary" :loading="dialogConfirmLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import Content_Api from "@/api/content";
import { GetInstrumentInputDTO } from "@/api/content/types";
import { useTableConfig } from "@/hooks/useTableConfig";

defineOptions({
  name: "DeviceTypeManagement",
});

const queryParams = ref<GetInstrumentInputDTO>({
  isEnable: null,
  keywords: "",
  page: 1,
  pageSize: 10,
});

const isPreview = ref<boolean>(false);
provide("isPreview", isPreview);

const {
  tableLoading,
  pageData,
  total,
  tableRef,
  exportLoading,
  tableDateFormat,
  tableFluidHeight,
  tableResize,
} = useTableConfig<BaseInstrument>();

const showDialog = ref<boolean>(false);
const dialogConfirmLoading = ref<boolean>(false);
const deviceTypeContentRef = useTemplateRef("deviceTypeContentRef");
const currentItemInfo = ref<BaseInstrument | null>(null);
const dialogTitle = ref<string>("");

const handleQuery = () => {
  queryParams.value.page = 1;
  handleGetTableList();
};

const handlePreviewOrEdit = async (row: BaseInstrument | null, isPreviewState: boolean) => {
  isPreview.value = row ? isPreviewState : false;
  dialogTitle.value = row ? (row.Id ? "查看" : "编辑") : "新增";
  if (row) {
    currentItemInfo.value = row;
  }
  showDialog.value = true;
};
const handleGetTableList = async () => {
  tableLoading.value = true;
  const res = await Content_Api.getInstrumentsPageData(queryParams.value);
  if (res.Type === 200) {
    pageData.value = res.Data.Data;
    total.value = res.Data.TotalCount;
  }
  tableLoading.value = false;
};

const handleDelete = (row: BaseInstrument) => {
  const Id = row.InstrumentId;
  ElMessageBox.confirm("此操作将删除该条数据, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      Content_Api.deleteInstrument({ instrumentId: Id }).then((data) => {
        if (data.Type === 200) {
          ElMessage.success(data.Content);
          handleGetTableList();
        } else {
          ElMessage.error(data.Content);
        }
      });
    })
    .catch((error) => {
      ElMessage.error(error.message || "删除失败");
    });
};

const handlePublish = (row: BaseInstrument) => {
  const Id = row.InstrumentId;
  ElMessageBox.confirm("此操作将删除该条数据, 是否继续?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      Content_Api.publishInstrument({ instrumentId: Id }).then((data) => {
        if (data.Type === 200) {
          ElMessage.success(data.Content);
          handleGetTableList();
        } else {
          ElMessage.error(data.Content);
        }
      });
    })
    .catch((error) => {
      ElMessage.error(error.message || "删除失败");
    });
};

onActivated(() => {
  handleGetTableList();
});
</script>

<style lang="scss" scoped></style>
